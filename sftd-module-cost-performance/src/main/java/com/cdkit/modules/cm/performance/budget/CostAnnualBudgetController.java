package com.cdkit.modules.cm.performance.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.budget.IAnnualBudgetApi;
import com.cdkit.modules.cm.api.budget.dto.BudgetSubjectInfoDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailDTO;
import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDetailImportDTO;
import com.cdkit.modules.cm.performance.budget.converter.CostAnnualBudgetDetailImportConverter;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.util.List;
import com.cdkit.modules.cm.application.budget.AnnualBudgetApplication;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.entity.ImportParams;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

/**
 * 年度总预算控制器
 * <AUTHOR>
 * @date 2025-07-30
 */
@Tag(name = "年度总预算管理")
@RestController
@RequestMapping("/cm/costAnnualBudget")
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetController implements IAnnualBudgetApi {

    private final AnnualBudgetApplication annualBudgetApplication;
    private final CostAnnualBudgetDetailImportConverter importConverter;

    @Override
    public Result<IPage<CostAnnualBudgetDTO>> queryPageList(CostAnnualBudgetDTO queryVO, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询年度总预算列表，页码: {}, 每页数量: {}", pageNo, pageSize);
        
        try {
            // 转换查询条件
            CostAnnualBudgetEntity queryEntity = CostAnnualBudgetConverter.toEntity(queryVO);
            
            // 分页查询
            PageRes<CostAnnualBudgetEntity> pageRes = annualBudgetApplication.queryPageList(queryEntity, pageNo, pageSize);
            
            // 使用 MyBatis Plus 的分页对象
            IPage<CostAnnualBudgetDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(CostAnnualBudgetConverter.toDTOList(pageRes.getRecords()));
            }
            
            log.info("分页查询年度总预算列表成功，总记录数: {}", page.getTotal());
            return Result.OK(page);
            
        } catch (Exception e) {
            log.error("分页查询年度总预算列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<CostAnnualBudgetDetailDTO> queryById(String id) {
        log.info("开始根据ID查询年度总预算详情（包含明细数据），ID: {}", id);

        try {
            // 查询主表数据
            CostAnnualBudgetEntity entity = annualBudgetApplication.queryById(id);
            if (entity == null) {
                log.warn("年度总预算不存在，ID: {}", id);
                return Result.error("年度总预算不存在");
            }

            // 查询明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                annualBudgetApplication.queryBudgetDetailsByBudgetId(id);

            // 转换为详情DTO
            CostAnnualBudgetDetailDTO detailDTO = CostAnnualBudgetDetailConverter.toDetailDTO(entity, budgetDetailList);

            log.info("根据ID查询年度总预算详情成功，ID: {}, 预算编号: {}, 明细数量: {}",
                    id, detailDTO.getBudgetCode(),
                    detailDTO.getBudgetDetailList() != null ? detailDTO.getBudgetDetailList().size() : 0);

            return Result.OK(detailDTO);

        } catch (Exception e) {
            log.error("根据ID查询年度总预算详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> edit(CostAnnualBudgetDTO costAnnualBudget) {
        log.info("开始编辑年度总预算，ID: {}, 预算名称: {}", 
                costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null");
        
        try {
            if (costAnnualBudget == null) {
                return Result.error("年度总预算数据不能为空");
            }
            
            CostAnnualBudgetEntity entity = CostAnnualBudgetConverter.toEntity(costAnnualBudget);
            String id = annualBudgetApplication.edit(entity);
            
            log.info("编辑年度总预算成功，ID: {}, 预算名称: {}", id, costAnnualBudget.getBudgetName());
            return Result.OK("编辑成功");
            
        } catch (Exception e) {
            log.error("编辑年度总预算失败，ID: {}, 预算名称: {}", 
                    costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                    costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> delete(String id) {
        log.info("开始根据ID删除年度总预算，ID: {}", id);
        
        try {
            annualBudgetApplication.delete(id);
            log.info("根据ID删除年度总预算成功，ID: {}", id);
            return Result.OK("删除成功");
            
        } catch (Exception e) {
            log.error("根据ID删除年度总预算失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> deleteBatch(String ids) {
        log.info("开始批量删除年度总预算，IDs: {}", ids);

        try {
            annualBudgetApplication.deleteBatch(ids);
            log.info("批量删除年度总预算成功，IDs: {}", ids);
            return Result.OK("批量删除成功");

        } catch (Exception e) {
            log.error("批量删除年度总预算失败，IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> generateNextBudgetCode() {
        log.info("开始生成下一个预算编号（当前年份）");

        try {
            String nextBudgetCode = annualBudgetApplication.generateNextBudgetCode();
            log.info("生成下一个预算编号成功，预算编号: {}", nextBudgetCode);
            return Result.OK(nextBudgetCode);

        } catch (Exception e) {
            log.error("生成下一个预算编号失败", e);
            return Result.error("生成预算编号失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> saveStep1(CostAnnualBudgetSaveRequest request) {
        log.info("开始保存年度预算第一步，预算名称: {}", request != null ? request.getBudgetName() : "null");

        try {
            if (request == null) {
                return Result.error("年度预算数据不能为空");
            }

            // 转换主表数据
            CostAnnualBudgetEntity mainEntity = CostAnnualBudgetSaveConverter.toEntity(request);

            // 转换明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                    CostAnnualBudgetSaveConverter.toBudgetDetailInfoList(request.getBudgetDetailList());

            // 调用应用服务保存
            String id = annualBudgetApplication.saveStep1(mainEntity, budgetDetailList);

            log.info("保存年度预算第一步成功，预算名称: {}, ID: {}", request.getBudgetName(), id);
            return Result.OK("保存成功",id);

        } catch (Exception e) {
            log.error("保存年度预算第一步失败，预算名称: {}", request != null ? request.getBudgetName() : "null", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> editStep1(CostAnnualBudgetSaveRequest request) {
        log.info("开始编辑年度预算第一步，预算ID: {}, 预算名称: {}",
                request != null ? request.getId() : "null",
                request != null ? request.getBudgetName() : "null");

        try {
            if (request == null) {
                return Result.error("年度预算数据不能为空");
            }

            if (!StringUtils.hasText(request.getId())) {
                return Result.error("年度预算ID不能为空");
            }

            // 转换主表数据
            CostAnnualBudgetEntity mainEntity = CostAnnualBudgetSaveConverter.toEntity(request);

            // 转换明细数据
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
                    CostAnnualBudgetSaveConverter.toBudgetDetailInfoList(request.getBudgetDetailList());

            // 调用应用服务编辑
            String id = annualBudgetApplication.editStep1(mainEntity, budgetDetailList);

            log.info("编辑年度预算第一步成功，预算名称: {}, ID: {}", request.getBudgetName(), id);
            return Result.OK("编辑成功", id);

        } catch (Exception e) {
            log.error("编辑年度预算第一步失败，预算ID: {}, 预算名称: {}",
                    request != null ? request.getId() : "null",
                    request != null ? request.getBudgetName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<BudgetSubjectInfoDTO>> queryBudgetSubjects(String projectPlanId) {
        log.info("开始查询预算科目信息，项目计划ID: {}", projectPlanId);

        try {
            // 调用应用服务查询预算科目信息
            List<AnnualBudgetApplication.BudgetSubjectInfo> subjectInfoList =
                    annualBudgetApplication.queryBudgetSubjects(projectPlanId);

            // 转换为DTO
            List<BudgetSubjectInfoDTO> resultList = new ArrayList<>();
            for (AnnualBudgetApplication.BudgetSubjectInfo info : subjectInfoList) {
                BudgetSubjectInfoDTO dto = new BudgetSubjectInfoDTO();
                BeanUtils.copyProperties(info, dto);
                resultList.add(dto);
            }

            log.info("查询预算科目信息成功，返回 {} 条记录", resultList.size());
            return Result.OK(resultList);

        } catch (Exception e) {
            log.error("查询预算科目信息失败，项目计划ID: {}", projectPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 年度预算明细导入Excel
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导入结果（CostAnnualBudgetSaveRequest格式）
     */
    @Operation(summary = "年度预算明细-导入Excel")
    @Override
    public Result<CostAnnualBudgetSaveRequest> importExcel(HttpServletRequest request, HttpServletResponse response) {
        log.info("开始导入年度预算明细Excel");

        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

            if (fileMap.isEmpty()) {
                log.warn("未找到上传的Excel文件");
                return Result.error("请选择要导入的Excel文件");
            }

            // 获取上传文件对象
            MultipartFile file = fileMap.values().iterator().next();
            if (file.isEmpty()) {
                log.warn("上传的Excel文件为空");
                return Result.error("上传的Excel文件为空");
            }

            log.info("开始解析Excel文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

            // 设置导入参数
            ImportParams params = new ImportParams();
            params.setTitleRows(1); // 标题行数：1行
            params.setHeadRows(1);  // 表头行数：1行（第2行是表头）
            params.setNeedSave(false); // 不需要保存到数据库

            // 使用cdkit的ExcelImportUtil解析Excel
            List<CostAnnualBudgetDetailImportDTO> importList = ExcelImportUtil.importExcel(
                    file.getInputStream(),
                    CostAnnualBudgetDetailImportDTO.class,
                    params
            );

            if (importList == null || importList.isEmpty()) {
                log.warn("Excel文件中没有有效数据");
                return Result.error("Excel文件中没有有效数据");
            }

            log.info("成功解析Excel文件，共 {} 条数据", importList.size());

            // 转换为CostAnnualBudgetSaveRequest格式
            CostAnnualBudgetSaveRequest saveRequest = importConverter.toSaveRequest(importList);

            if (saveRequest == null) {
                log.error("数据转换失败");
                return Result.error("数据转换失败");
            }

            log.info("年度预算明细导入成功，共 {} 条明细数据",
                    saveRequest.getBudgetDetailList() != null ? saveRequest.getBudgetDetailList().size() : 0);

            return Result.OK("导入成功", saveRequest);

        } catch (Exception e) {
            log.error("年度预算明细导入失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

}
